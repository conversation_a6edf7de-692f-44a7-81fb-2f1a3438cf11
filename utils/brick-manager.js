/**
 * 统一积木数据管理器
 * 整合所有积木数据的获取、存储、同步逻辑
 */

class BrickManager {
  constructor() {
    this.initialized = false;
    this.bricks = [];
    this.lastSyncTime = null;
    this.syncInProgress = false;
  }

  /**
   * 初始化积木管理器
   */
  async init() {
    if (this.initialized) return;

    console.log('🧱 初始化积木数据管理器...');

    try {
      await this.loadBricks();
      this.initialized = true;
      console.log('✅ 积木数据管理器初始化完成');
    } catch (error) {
      console.error('❌ 积木数据管理器初始化失败:', error);
    }
  }

  /**
   * 统一加载积木数据
   * 优先级：云数据库 > 本地存储（离线缓存） > resumeTasks提取 > 默认数据
   */
  async loadBricks() {
    console.log('📦 开始加载积木数据...');

    // 1. 检查用户登录状态，决定是否尝试云数据库
    const isLoggedIn = await this.checkUserLoginStatus();

    if (isLoggedIn) {
      // 用户已登录，尝试从云数据库获取最新数据
      try {
        const cloudBricks = await this.loadFromCloudDatabase();
        if (cloudBricks.length > 0) {
          this.bricks = this.normalizeBricks(cloudBricks);
          console.log(`✅ 从云数据库获取积木: ${this.bricks.length} 个`);
          this.saveToLocal(); // 更新本地缓存
          this.updateGlobalData();
          return this.bricks;
        }
      } catch (error) {
        console.warn('⚠️ 云数据库获取积木失败，尝试本地缓存:', error);
        // 如果是身份验证失败，记录状态但不阻断流程
        if (error.message && error.message.includes('身份验证失败')) {
          console.warn('⚠️ 用户身份验证失败，将使用本地缓存模式');
          // 显示用户友好的提示
          this.showAuthFailureToast();
        }
      }
    } else {
      console.log('⚠️ 用户未登录，跳过云数据库查询，使用本地缓存');
    }

    // 2. 网络异常或未登录时使用本地缓存
    try {
      const localBricks = wx.getStorageSync('bricks');
      if (localBricks && localBricks.length > 0) {
        this.bricks = this.normalizeBricks(localBricks);
        console.log(`✅ 从本地缓存获取积木: ${this.bricks.length} 个`);
        this.updateGlobalData();
        // 如果用户已登录，后台尝试同步云数据库
        if (isLoggedIn) {
          this.syncToCloud().catch(err => console.warn('后台同步失败:', err));
        }
        return this.bricks;
      }
    } catch (error) {
      console.warn('⚠️ 本地存储获取积木失败:', error);
    }

    // 3. 从resumeTasks提取积木数据（数据迁移）
    try {
      const extractedBricks = await this.extractBricksFromTasks();
      if (extractedBricks.length > 0) {
        this.bricks = this.normalizeBricks(extractedBricks);
        console.log(`✅ 从resumeTasks提取积木: ${this.bricks.length} 个`);
        // 如果用户已登录，立即同步到云数据库
        if (isLoggedIn) {
          await this.syncToCloud();
        }
        this.saveToLocal();
        this.updateGlobalData();
        return this.bricks;
      }
    } catch (error) {
      console.warn('⚠️ 从resumeTasks提取积木失败:', error);
    }

    // 4. 使用默认积木数据
    console.log('🔄 使用默认积木数据');
    this.bricks = this.normalizeBricks(this.getDefaultBricks());
    this.updateGlobalData();
    return this.bricks;
  }

  /**
   * 从resumeTasks中提取积木数据（核心功能）
   */
  async extractBricksFromTasks() {
    console.log('🔍 开始从resumeTasks提取积木数据...');

    const userId = wx.getStorageSync('userId') || wx.getStorageSync('openid');
    if (!userId) {
      console.warn('⚠️ 未找到用户ID，无法提取积木数据');
      return [];
    }

    try {
      const db = wx.cloud.database();
      const tasksResult = await db.collection('resumeTasks').where({
        userId: userId,
        status: 'completed'
      }).orderBy('completedAt', 'desc').get();

      console.log(`📋 找到 ${tasksResult.data.length} 个已完成的任务`);

      const extractedBricks = [];
      for (const task of tasksResult.data) {
        if (task.result && task.result.data && task.result.data.bricks) {
          const taskBricks = task.result.data.bricks.map(brick => ({
            ...brick,
            // 确保必要字段
            id: brick.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            sourceTaskId: task.taskId,
            extractedAt: new Date().toISOString(),
            userId: userId
          }));
          extractedBricks.push(...taskBricks);
        }
      }

      console.log(`🎯 成功提取 ${extractedBricks.length} 个积木`);
      return extractedBricks;

    } catch (error) {
      console.error('❌ 从resumeTasks提取积木失败:', error);
      return [];
    }
  }

  /**
   * 从云数据库bricks集合加载（通过云函数）
   */
  async loadFromCloudDatabase() {
    try {
      console.log('☁️ 通过云函数加载积木数据...');

      // 在调用云函数前，确保用户身份验证有效
      const authValid = await this.ensureCloudAuth();
      if (!authValid) {
        console.warn('⚠️ 云开发身份验证失败，但继续尝试调用云函数...');
        // 不直接抛出错误，而是尝试调用云函数，让云函数返回具体的错误信息
      }

      const result = await wx.cloud.callFunction({
        name: 'brickManager',
        data: {
          action: 'list',
          data: {
            limit: 100,
            sortBy: 'updateTime',
            sortOrder: 'desc'
          }
        }
      });

      // 详细调试日志
      console.log('🔍 云函数原始返回结果:', {
        errMsg: result.errMsg,
        requestID: result.requestID,
        result: result.result
      });

      // 使用通用解析方法
      const responseData = this.parseCloudFunctionResult(result, '加载积木数据');
      const bricks = responseData.data?.bricks || [];
      console.log(`✅ 云函数返回 ${bricks.length} 个积木`);
      return bricks;

    } catch (error) {
      console.error('❌ 云数据库加载积木失败:', error);
      console.error('❌ 错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      // 如果是身份验证失败，提供用户友好的错误提示
      if (error.message && error.message.includes('身份验证失败')) {
        console.warn('⚠️ 检测到身份验证失败，尝试重新建立身份...');

        // 尝试重新建立身份验证
        const reAuthSuccess = await this.establishCloudAuth();
        if (reAuthSuccess) {
          console.log('🔄 身份验证重新建立成功，重试加载积木...');
          // 递归重试一次
          try {
            return await this.loadFromCloudDatabase();
          } catch (retryError) {
            console.error('❌ 重试加载积木仍然失败:', retryError);
            this.showAuthFailureDialog();
          }
        } else {
          this.showAuthFailureDialog();
        }
      }

      return [];
    }
  }

  /**
   * 确保云开发身份验证有效
   */
  async ensureCloudAuth() {
    try {
      console.log('🔐 确保云开发身份验证有效...');

      // 首先验证当前身份
      const authValid = await this.verifyCloudAuth();
      if (authValid) {
        return true;
      }

      // 如果身份验证失败，尝试建立微信云开发身份
      console.log('🔄 身份验证失败，尝试建立微信云开发身份...');
      const cloudAuthSuccess = await this.establishCloudAuth();

      if (cloudAuthSuccess) {
        // 重新验证身份
        return await this.verifyCloudAuth();
      }

      // 如果还是失败，尝试重新登录
      console.log('🔄 建立云开发身份失败，尝试重新登录...');
      const reloginSuccess = await this.attemptAutoRelogin();

      if (reloginSuccess) {
        // 重新验证身份
        return await this.verifyCloudAuth();
      }

      return false;
    } catch (error) {
      console.error('❌ 确保云开发身份验证失败:', error);
      return false;
    }
  }

  /**
   * 显示身份验证失败对话框
   */
  showAuthFailureDialog() {
    wx.showModal({
      title: '需要重新登录',
      content: '您的登录状态已过期，积木功能需要重新登录才能使用。重新登录后可以同步您的积木数据。',
      confirmText: '重新登录',
      cancelText: '使用离线模式',
      success: (res) => {
        if (res.confirm) {
          // 跳转到登录页面
          wx.navigateTo({
            url: '/pages/login/login?reason=auth_expired&redirect=bricks'
          });
        } else {
          // 用户选择使用离线模式
          console.log('🔄 用户选择使用离线模式');
          wx.showToast({
            title: '已切换到离线模式',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  }

  /**
   * 显示身份验证失败的轻提示
   */
  showAuthFailureToast() {
    wx.showToast({
      title: '登录已过期，已切换到离线模式',
      icon: 'none',
      duration: 3000
    });
  }

  /**
   * 获取默认积木数据
   * 🔧 修复：不再生成默认的个人信息积木，避免显示多余的占位内容
   */
  getDefaultBricks() {
    // 返回空数组，不生成任何默认积木
    // 当用户上传简历或手动添加积木时，才会有真实的积木数据
    return [];
  }

  /**
   * 保存到本地存储
   */
  saveToLocal() {
    try {
      wx.setStorageSync('bricks', this.bricks);
      wx.setStorageSync('bricks_sync_time', new Date().toISOString());
      console.log('✅ 积木数据已保存到本地存储');
    } catch (error) {
      console.error('❌ 保存积木到本地存储失败:', error);
    }
  }

  /**
   * 更新全局数据
   */
  updateGlobalData() {
    try {
      const app = getApp();
      if (app.globalData) {
        app.globalData.bricks = this.bricks;
        console.log('✅ 积木数据已更新到全局数据');
      }
      if (app.store) {
        app.store.setState('bricks.list', this.bricks);
        console.log('✅ 积木数据已更新到状态管理器');
      }
    } catch (error) {
      console.error('❌ 更新全局数据失败:', error);
    }
  }

  /**
   * 检查用户登录状态（统一身份验证系统）
   */
  async checkUserLoginStatus() {
    try {
      console.log('🔍 开始统一身份验证检查...');

      // 1. 检查JWT登录状态
      const jwtValid = this.checkJWTLoginStatus();
      if (!jwtValid) {
        console.log('❌ JWT登录状态无效');
        return false;
      }

      console.log('✅ JWT登录状态有效');

      // 2. 检查微信云开发身份
      const cloudAuthValid = await this.verifyCloudAuth();
      if (cloudAuthValid) {
        console.log('✅ 微信云开发身份验证有效');
        return true;
      }

      // 3. JWT有效但云开发身份无效，尝试重新建立
      console.log('⚠️ JWT有效但云开发身份无效，尝试重新建立...');
      const reestablishSuccess = await this.establishCloudAuth();

      if (reestablishSuccess) {
        console.log('✅ 云开发身份重新建立成功');
        return true;
      } else {
        console.warn('❌ 云开发身份重新建立失败');
        return false;
      }

    } catch (error) {
      console.error('❌ 统一身份验证检查失败:', error);
      return false;
    }
  }

  /**
   * 检查JWT登录状态
   */
  checkJWTLoginStatus() {
    try {
      // 检查全局数据
      const app = getApp();
      if (app.globalData && app.globalData.isLoggedIn) {
        console.log('✅ 全局数据显示JWT已登录');
        return true;
      }

      // 检查本地存储
      const isLoggedIn = wx.getStorageSync('isLoggedIn');
      const userInfo = wx.getStorageSync('userInfo');
      const sessionToken = wx.getStorageSync('session_token');

      if (isLoggedIn && userInfo && sessionToken) {
        console.log('✅ 本地存储显示JWT已登录');

        // 更新全局状态
        if (app.globalData) {
          app.globalData.isLoggedIn = true;
          app.globalData.userInfo = userInfo;
          app.globalData.sessionToken = sessionToken;
        }

        return true;
      }

      console.log('❌ JWT登录状态检查失败', {
        isLoggedIn: isLoggedIn,
        hasUserInfo: !!userInfo,
        hasSessionToken: !!sessionToken
      });
      return false;
    } catch (error) {
      console.error('❌ JWT登录状态检查异常:', error);
      return false;
    }
  }

  /**
   * 验证微信云开发身份
   */
  async verifyCloudAuth() {
    try {
      console.log('🔐 验证微信云开发身份...');

      // 尝试调用一个简单的云函数来验证身份
      const result = await wx.cloud.callFunction({
        name: 'ping',
        data: { action: 'auth-check' }
      });

      if (result && result.result && result.result.success) {
        console.log('✅ 微信云开发身份验证成功，OPENID:', result.result.openid);
        return true;
      } else {
        console.warn('⚠️ 微信云开发身份验证失败，结果:', result);
        return false;
      }
    } catch (error) {
      console.warn('⚠️ 微信云开发身份验证异常:', error.message);

      // 如果是身份验证失败，尝试自动重新登录
      if (error.message && error.message.includes('身份验证失败')) {
        console.log('🔄 检测到身份验证失败，尝试自动重新登录...');
        await this.attemptAutoRelogin();
      }

      return false;
    }
  }

  /**
   * 建立微信云开发身份验证
   */
  async establishCloudAuth() {
    try {
      console.log('🔐 建立微信云开发身份验证...');

      // 检查云开发是否已初始化
      if (!wx.cloud) {
        console.error('❌ 云开发未初始化');
        return false;
      }

      // 首先尝试匿名登录到云开发
      try {
        console.log('🔄 尝试匿名登录到云开发...');
        await wx.cloud.auth().signInAnonymously();
        console.log('✅ 匿名登录成功');
      } catch (authError) {
        console.warn('⚠️ 匿名登录失败，但继续尝试:', authError);
        // 不直接返回false，继续尝试其他方法
      }

      // 尝试通过调用云函数来验证身份
      console.log('🔄 通过调用ping云函数验证身份...');
      const result = await wx.cloud.callFunction({
        name: 'ping',
        data: { action: 'establish-auth' }
      });

      if (result && result.result && result.result.success) {
        console.log('✅ 微信云开发身份建立成功，OPENID:', result.result.openid ? result.result.openid.substring(0, 8) + '...' : 'null');

        // 存储云开发身份信息
        if (result.result.openid) {
          wx.setStorageSync('cloud_openid', result.result.openid);

          // 更新全局状态
          const app = getApp();
          if (app.globalData) {
            app.globalData.cloudOpenid = result.result.openid;
            app.globalData.isCloudConnected = true;
          }
        }

        return true;
      } else {
        console.warn('⚠️ 微信云开发身份建立失败，结果:', result);
        return false;
      }

    } catch (error) {
      console.error('❌ 建立微信云开发身份失败:', error);

      // 如果是网络错误，可能是临时问题
      if (error.message && error.message.includes('network')) {
        console.log('🔄 检测到网络错误，稍后重试...');
      }

      return false;
    }
  }

  /**
   * 尝试自动重新登录微信云开发
   */
  async attemptAutoRelogin() {
    try {
      console.log('🔄 开始自动重新登录微信云开发...');

      // 使用微信登录获取新的身份上下文
      const loginResult = await new Promise((resolve, reject) => {
        wx.login({
          success: (res) => {
            if (res.code) {
              resolve(res.code);
            } else {
              reject(new Error('获取微信登录码失败'));
            }
          },
          fail: reject
        });
      });

      console.log('✅ 自动重新登录成功，获取到新的登录码');

      // 更新登录状态
      const app = getApp();
      if (app.globalData) {
        app.globalData.lastAutoRelogin = Date.now();
      }

      return true;
    } catch (error) {
      console.error('❌ 自动重新登录失败:', error);
      return false;
    }
  }

  /**
   * 提示用户登录
   */
  promptUserLogin() {
    console.log('🔐 提示用户登录');
    wx.showModal({
      title: '需要登录',
      content: '使用积木功能需要先登录，是否前往登录页面？',
      confirmText: '去登录',
      cancelText: '稍后',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      }
    });
  }

  /**
   * 解析云函数返回结果的通用方法
   */
  parseCloudFunctionResult(result, operation = '云函数调用') {
    try {
      console.log(`🔍 ${operation}原始返回:`, result);

      let responseData = null;

      // 情况1: HTTP响应格式 (包含statusCode, headers, body)
      if (result.result && result.result.statusCode) {
        console.log(`📦 ${operation}检测到HTTP响应格式，statusCode:`, result.result.statusCode);

        if (result.result.statusCode === 200 && result.result.body) {
          responseData = typeof result.result.body === 'string'
            ? JSON.parse(result.result.body)
            : result.result.body;
        } else {
          // 解析错误响应
          let errorMessage = `HTTP错误: ${result.result.statusCode}`;
          if (result.result.body) {
            try {
              const errorBody = typeof result.result.body === 'string'
                ? JSON.parse(result.result.body)
                : result.result.body;
              errorMessage = errorBody.error || errorMessage;
            } catch (e) {
              console.warn('无法解析错误响应body');
            }
          }
          throw new Error(errorMessage);
        }
      }
      // 情况2: 直接的JSON格式 (包含success, data, error)
      else if (result.result && result.result.hasOwnProperty('success')) {
        console.log(`📦 ${operation}检测到直接JSON格式`);
        responseData = result.result;
      }
      // 情况3: 其他格式或错误
      else {
        console.error(`❌ ${operation}返回格式异常:`, result);
        throw new Error('云函数返回格式异常');
      }

      // 验证响应数据
      if (responseData && responseData.success) {
        console.log(`✅ ${operation}成功:`, responseData);
        return responseData;
      } else {
        const errorMsg = responseData?.error || '未知错误';
        console.error(`❌ ${operation}失败:`, errorMsg);
        throw new Error(errorMsg);
      }

    } catch (error) {
      console.error(`❌ 解析${operation}结果失败:`, error);
      throw error;
    }
  }

  /**
   * 添加新积木（优化版本，减少降级策略依赖）
   */
  async addBrick(brickData) {
    try {
      console.log('➕ 添加新积木:', brickData);

      // 检查用户登录状态
      const isLoggedIn = await this.checkUserLoginStatus();
      if (!isLoggedIn) {
        console.warn('⚠️ 用户未登录，尝试匿名登录...');
        // 尝试匿名登录而不是立即降级
        try {
          await this.establishCloudAuth();
        } catch (authError) {
          console.warn('⚠️ 匿名登录失败，使用本地存储降级方案');
          return this.addBrickLocally(brickData);
        }
      }

      // 多次重试云开发身份验证
      let authValid = false;
      let retryCount = 0;
      const maxRetries = 3;

      while (!authValid && retryCount < maxRetries) {
        try {
          authValid = await this.ensureCloudAuth();
          if (!authValid) {
            console.log(`🔄 身份验证失败，重试 ${retryCount + 1}/${maxRetries}...`);
            await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // 递增延迟
            retryCount++;
          }
        } catch (authError) {
          console.warn(`⚠️ 身份验证重试 ${retryCount + 1} 失败:`, authError);
          retryCount++;
        }
      }

      if (!authValid) {
        console.warn('⚠️ 多次重试后身份验证仍然失败，使用本地存储降级方案');
        return this.addBrickLocally(brickData);
      }

      // 调用云函数添加积木
      const result = await wx.cloud.callFunction({
        name: 'brickManager',
        data: {
          action: 'add',
          data: brickData
        }
      });

      // 使用通用解析方法
      const responseData = this.parseCloudFunctionResult(result, '添加积木');

      // 更新本地数据
      this.bricks.unshift(responseData.data.brick);
      this.saveToLocal();
      this.updateGlobalData();

      console.log('✅ 积木添加成功，已保存到云数据库');

      // 显示成功提示
      wx.showToast({
        title: '积木保存成功',
        icon: 'success',
        duration: 2000
      });

      return responseData.data.brick;
    } catch (error) {
      console.error('❌ 添加积木失败:', error);

      // 如果是身份验证失败，提供降级方案
      if (error.message && error.message.includes('身份验证失败')) {
        console.log('🔄 最终身份验证失败，使用本地存储降级方案');
        this.showAuthFailureToast();
        return this.addBrickLocally(brickData);
      }

      // 其他错误也使用降级方案，但记录详细错误信息
      console.error('❌ 云端保存失败，使用本地存储降级方案:', error);
      return this.addBrickLocally(brickData);
    }
  }

  /**
   * 本地添加积木（降级方案）
   */
  addBrickLocally(brickData) {
    try {
      const now = new Date();
      const brick = {
        ...brickData,
        id: brickData.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createTime: now.toISOString(),
        updateTime: now.toISOString(),
        usageCount: brickData.usageCount || 0,
        isActive: true,
        syncStatus: 'pending' // 标记为待同步
      };

      // 更新本地数据
      this.bricks.unshift(brick);
      this.saveToLocal();
      this.updateGlobalData();

      console.log('✅ 积木已添加到本地，待云端同步');

      // 提示用户
      wx.showToast({
        title: '已保存到本地，稍后同步云端',
        icon: 'success',
        duration: 2000
      });

      // 后台尝试同步到云端
      setTimeout(() => {
        this.syncPendingBricksToCloud().catch(err => {
          console.warn('⚠️ 后台同步失败:', err);
        });
      }, 2000);

      return brick;
    } catch (error) {
      console.error('❌ 本地添加积木失败:', error);
      throw error;
    }
  }

  /**
   * 更新积木
   */
  async updateBrick(brickId, updateData) {
    try {
      console.log('✏️ 更新积木:', brickId, updateData);

      // 先更新本地数据
      const index = this.bricks.findIndex(brick => brick.id === brickId);
      if (index !== -1) {
        this.bricks[index] = { ...this.bricks[index], ...updateData, updateTime: new Date().toISOString() };
        this.saveToLocal();
        this.updateGlobalData();
      }

      // 检查用户登录状态
      if (!this.checkUserLoginStatus()) {
        console.warn('⚠️ 用户未登录，积木已更新到本地，待登录后同步');
        // 标记为待同步
        if (index !== -1) {
          this.bricks[index].syncStatus = 'pending';
          this.saveToLocal();
        }
        return true;
      }

      // 调用云函数更新积木
      const result = await wx.cloud.callFunction({
        name: 'brickManager',
        data: {
          action: 'update',
          data: {
            id: brickId,
            ...updateData
          }
        }
      });

      // 使用通用解析方法
      const responseData = this.parseCloudFunctionResult(result, '更新积木');

      console.log('✅ 积木更新成功');
      return true;
    } catch (error) {
      console.error('❌ 更新积木失败:', error);

      // 如果是身份验证失败，本地数据已经更新，不抛出错误
      if (error.message && error.message.includes('身份验证失败')) {
        console.log('🔄 身份验证失败，积木已保存到本地');
        return true;
      }

      throw error;
    }
  }

  /**
   * 删除积木
   */
  async deleteBrick(brickId) {
    try {
      console.log('🗑️ 删除积木:', brickId);

      // 先更新本地数据
      this.bricks = this.bricks.filter(brick => brick.id !== brickId);
      this.saveToLocal();
      this.updateGlobalData();

      // 检查用户登录状态
      if (!this.checkUserLoginStatus()) {
        console.warn('⚠️ 用户未登录，积木已从本地删除');
        return true;
      }

      // 调用云函数删除积木
      const result = await wx.cloud.callFunction({
        name: 'brickManager',
        data: {
          action: 'delete',
          data: {
            id: brickId
          }
        }
      });

      // 使用通用解析方法
      const responseData = this.parseCloudFunctionResult(result, '删除积木');

      console.log('✅ 积木删除成功');
      return true;
    } catch (error) {
      console.error('❌ 删除积木失败:', error);

      // 如果是身份验证失败，本地数据已经删除，不抛出错误
      if (error.message && error.message.includes('身份验证失败')) {
        console.log('🔄 身份验证失败，积木已从本地删除');
        return true;
      }

      throw error;
    }
  }

  /**
   * 同步本地积木数据到云端
   */
  async syncToCloud() {
    if (this.syncInProgress) {
      console.log('⚠️ 同步正在进行中，跳过');
      return;
    }

    // 检查用户登录状态
    if (!this.checkUserLoginStatus()) {
      console.warn('⚠️ 用户未登录，无法同步到云端');
      return { success: false, message: '用户未登录' };
    }

    try {
      this.syncInProgress = true;
      console.log('🔄 开始同步积木数据到云端...');

      // 调用云函数同步数据
      const result = await wx.cloud.callFunction({
        name: 'brickManager',
        data: {
          action: 'sync',
          data: {
            bricks: this.bricks,
            mode: 'merge' // 合并模式，不覆盖云端数据
          }
        }
      });

      // 使用通用解析方法
      const responseData = this.parseCloudFunctionResult(result, '同步积木数据');

      this.lastSyncTime = new Date().toISOString();
      wx.setStorageSync('bricks_last_sync', this.lastSyncTime);

      // 清除待同步标记
      this.bricks.forEach(brick => {
        if (brick.syncStatus === 'pending') {
          delete brick.syncStatus;
        }
      });
      this.saveToLocal();

      console.log('✅ 积木数据同步成功:', responseData.data);
      return responseData.data;
    } catch (error) {
      console.error('❌ 同步积木数据失败:', error);

      // 如果是身份验证失败，不抛出错误，只记录日志
      if (error.message && error.message.includes('身份验证失败')) {
        console.log('🔄 身份验证失败，同步已跳过');
        return { success: false, message: '身份验证失败' };
      }

      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 强制刷新积木数据（从云端重新加载）
   */
  async refreshFromCloud() {
    try {
      console.log('🔄 强制从云端刷新积木数据...');

      const cloudBricks = await this.loadFromCloudDatabase();
      this.bricks = this.normalizeBricks(cloudBricks);
      this.saveToLocal();
      this.updateGlobalData();

      console.log(`✅ 刷新完成，获取到 ${this.bricks.length} 个积木`);
      return this.bricks;
    } catch (error) {
      console.error('❌ 刷新积木数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取积木统计信息
   */
  getStatistics() {
    const stats = {
      total: this.bricks.length,
      categories: {},
      lastUpdate: null,
      pendingSync: 0
    };

    this.bricks.forEach(brick => {
      // 统计分类
      if (brick.category) {
        stats.categories[brick.category] = (stats.categories[brick.category] || 0) + 1;
      }

      // 统计待同步数量
      if (brick.syncStatus === 'pending') {
        stats.pendingSync++;
      }

      // 找到最新更新时间
      if (brick.updateTime) {
        const updateTime = new Date(brick.updateTime);
        if (!stats.lastUpdate || updateTime > new Date(stats.lastUpdate)) {
          stats.lastUpdate = brick.updateTime;
        }
      }
    });

    return stats;
  }

  /**
   * 验证积木数据是否有效
   */
  validateBrickData(brick) {
    const errors = [];

    // 检查必要字段
    if (!brick.title || typeof brick.title !== 'string' || brick.title.trim() === '') {
      errors.push('标题为空或无效');
    }

    if (!brick.content || typeof brick.content !== 'string' || brick.content.trim() === '') {
      errors.push('内容为空或无效');
    }

    if (!brick.category || typeof brick.category !== 'string' || brick.category.trim() === '') {
      errors.push('分类为空或无效');
    }

    // 检查无效内容
    const invalidValues = ['暂无积木数据', '暂无数据', '', 'undefined', 'null'];
    if (invalidValues.includes(brick.title?.trim())) {
      errors.push('标题包含无效值');
    }

    if (invalidValues.includes(brick.content?.trim())) {
      errors.push('内容包含无效值');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 清理和标准化积木数据
   */
  sanitizeBrickData(brick) {
    const now = new Date().toISOString();

    return {
      ...brick,
      id: brick.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: (brick.title || '').toString().trim(),
      content: (brick.content || '').toString().trim(),
      category: (brick.category || 'skill').toString().trim(),
      description: (brick.description || brick.title || '').toString().trim(),
      tags: Array.isArray(brick.tags) ? brick.tags : [],
      usageCount: Number(brick.usageCount) || 0,
      createTime: brick.createTime || now,
      updateTime: now,
      isActive: brick.isActive !== false,
      userId: brick.userId || wx.getStorageSync('userId') || wx.getStorageSync('openid') || ''
    };
  }

  /**
   * 同步待同步的积木到云端
   */
  async syncPendingBricksToCloud() {
    try {
      const pendingBricks = this.bricks.filter(brick => brick.syncStatus === 'pending');
      if (pendingBricks.length === 0) {
        console.log('📦 没有待同步的积木');
        return;
      }

      console.log(`🔄 开始同步 ${pendingBricks.length} 个待同步积木到云端...`);

      // 确保身份验证有效
      const authValid = await this.ensureCloudAuth();
      if (!authValid) {
        console.warn('⚠️ 身份验证失败，跳过同步');
        return;
      }

      let successCount = 0;
      let skipCount = 0;

      for (const brick of pendingBricks) {
        try {
          console.log(`🔍 验证积木数据: ${brick.title || '未知标题'}`);

          // 1. 清理和标准化数据
          const sanitizedBrick = this.sanitizeBrickData(brick);

          // 2. 验证数据有效性
          const validation = this.validateBrickData(sanitizedBrick);

          if (!validation.isValid) {
            console.error(`❌ 积木数据验证失败: ${brick.title || '未知标题'}`, {
              errors: validation.errors,
              brickData: {
                id: brick.id,
                title: brick.title,
                content: brick.content?.substring(0, 50) + '...',
                category: brick.category
              }
            });

            // 移除无效的待同步积木
            const index = this.bricks.findIndex(b => b.id === brick.id);
            if (index !== -1) {
              delete this.bricks[index].syncStatus;
              console.log(`🗑️ 已移除无效积木的待同步标记: ${brick.title || '未知标题'}`);
            }

            skipCount++;
            continue;
          }

          console.log(`✅ 积木数据验证通过: ${sanitizedBrick.title}`);

          // 3. 调用云函数同步
          const result = await wx.cloud.callFunction({
            name: 'brickManager',
            data: {
              action: 'add',
              data: sanitizedBrick
            }
          });

          const responseData = this.parseCloudFunctionResult(result, '同步积木');

          // 4. 移除待同步标记并更新本地数据
          const index = this.bricks.findIndex(b => b.id === brick.id);
          if (index !== -1) {
            // 更新本地积木数据为清理后的版本
            this.bricks[index] = { ...sanitizedBrick };
            delete this.bricks[index].syncStatus;
          }

          successCount++;
          console.log(`✅ 积木 ${sanitizedBrick.title} 同步成功`);

        } catch (error) {
          console.error(`❌ 积木 ${brick.title || '未知标题'} 同步失败:`, {
            error: error.message,
            brickId: brick.id,
            brickTitle: brick.title
          });

          // 如果是验证错误，移除待同步标记避免重复尝试
          if (error.message && error.message.includes('不能为空')) {
            const index = this.bricks.findIndex(b => b.id === brick.id);
            if (index !== -1) {
              delete this.bricks[index].syncStatus;
              console.log(`🗑️ 已移除验证失败积木的待同步标记: ${brick.title || '未知标题'}`);
            }
            skipCount++;
          }
        }
      }

      // 保存更新后的数据
      if (successCount > 0 || skipCount > 0) {
        this.saveToLocal();
        this.updateGlobalData();
      }

      // 显示同步结果
      const totalProcessed = successCount + skipCount;
      console.log(`📊 积木同步完成: 成功${successCount}个, 跳过${skipCount}个, 总计${totalProcessed}/${pendingBricks.length}个`);

      if (successCount > 0) {
        const message = skipCount > 0
          ? `${successCount}个积木已同步，${skipCount}个积木数据无效已跳过`
          : `${successCount}个积木已同步到云端`;

        wx.showToast({
          title: message,
          icon: successCount === pendingBricks.length ? 'success' : 'none',
          duration: 3000
        });
      } else if (skipCount > 0) {
        wx.showToast({
          title: `${skipCount}个积木数据无效，已清理`,
          icon: 'none',
          duration: 3000
        });
      }

      return {
        success: successCount > 0,
        successCount,
        skipCount,
        totalCount: pendingBricks.length
      };

    } catch (error) {
      console.error('❌ 同步待同步积木失败:', error);

      wx.showToast({
        title: '积木同步失败，请稍后重试',
        icon: 'error',
        duration: 2000
      });

      throw error;
    }
  }

  /**
   * 用户登录后的数据同步处理
   */
  async onUserLogin() {
    try {
      console.log('🔐 用户登录，开始处理积木数据同步...');

      // 1. 检查是否有待同步的本地数据
      const pendingBricks = this.bricks.filter(brick => brick.syncStatus === 'pending');
      if (pendingBricks.length > 0) {
        console.log(`📦 发现 ${pendingBricks.length} 个待同步的积木`);

        // 同步本地数据到云端
        await this.syncPendingBricksToCloud();
      }

      // 2. 从云端加载最新数据
      const cloudBricks = await this.loadFromCloudDatabase();
      if (cloudBricks.length > 0) {
        // 合并云端数据和本地数据
        this.mergeCloudAndLocalData(cloudBricks);
      }

      console.log('✅ 用户登录后数据同步完成');
    } catch (error) {
      console.error('❌ 用户登录后数据同步失败:', error);
    }
  }

  /**
   * 合并云端数据和本地数据
   */
  mergeCloudAndLocalData(cloudBricks) {
    try {
      console.log('🔄 合并云端数据和本地数据...');

      const mergedBricks = [...cloudBricks];
      const cloudBrickIds = new Set(cloudBricks.map(brick => brick.id));

      // 添加本地独有的积木
      this.bricks.forEach(localBrick => {
        if (!cloudBrickIds.has(localBrick.id)) {
          mergedBricks.push(localBrick);
        }
      });

      this.bricks = this.normalizeBricks(mergedBricks);
      this.saveToLocal();
      this.updateGlobalData();

      console.log(`✅ 数据合并完成，共 ${this.bricks.length} 个积木`);
    } catch (error) {
      console.error('❌ 数据合并失败:', error);
    }
  }

  /**
   * 标准化积木数据结构
   */
  normalizeBrick(brick) {
    // 检查是否是无效的内容
    const isInvalidContent = (content) => {
      if (!content || typeof content !== 'string') return true;
      const invalidTexts = ['暂无积木数据', '暂无数据', '无数据', ''];
      return invalidTexts.includes(content.trim());
    };

    // 根据分类生成默认标题和描述
    const getDefaultTitleAndDesc = (category) => {
      const defaults = {
        'personal': {
          title: '个人信息',
          description: '请上传简历或手动添加个人信息，包括姓名、联系方式、地址等基本信息'
        },
        'education': {
          title: '教育背景',
          description: '请添加您的教育经历，包括学校、专业、学历等信息'
        },
        'experience': {
          title: '工作经历',
          description: '请添加您的工作经验，包括公司、职位、工作内容等信息'
        },
        'project': {
          title: '项目经验',
          description: '请添加您的项目经历，包括项目名称、技术栈、成果等信息'
        },
        'skills': {
          title: '技能能力',
          description: '请添加您的技能和能力，包括技术技能、软技能等'
        }
      };

      return defaults[category] || {
        title: '能力积木',
        description: '请添加您的能力和经验描述'
      };
    };

    // 获取有效的标题
    const getValidTitle = () => {
      if (brick.title && !isInvalidContent(brick.title)) {
        return brick.title;
      }
      if (brick.content && !isInvalidContent(brick.content)) {
        return brick.content;
      }
      return getDefaultTitleAndDesc(brick.category).title;
    };

    // 获取有效的描述
    const getValidDescription = () => {
      if (brick.description && !isInvalidContent(brick.description)) {
        return brick.description;
      }
      if (brick.content && !isInvalidContent(brick.content) && brick.content !== getValidTitle()) {
        return brick.content;
      }
      return getDefaultTitleAndDesc(brick.category).description;
    };

    return {
      ...brick,
      // 确保必要字段存在且有效
      id: brick.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: getValidTitle(),
      description: getValidDescription(),
      category: brick.category || 'other',
      keywords: brick.keywords || [],
      confidence: brick.confidence || 0.8,
      usageCount: brick.usageCount || 0,
      level: brick.level || '基础',
      createTime: brick.createTime || new Date().toISOString(),
      updateTime: brick.updateTime || new Date().toISOString(),
      source: brick.source || 'unknown'
    };
  }

  /**
   * 标准化积木数组
   */
  normalizeBricks(bricks) {
    if (!Array.isArray(bricks)) return [];
    return bricks.map(brick => this.normalizeBrick(brick));
  }

  /**
   * 获取积木数据
   */
  async getBricks() {
    if (!this.initialized) {
      await this.init();
    }
    // 返回前进行数据标准化
    this.bricks = this.normalizeBricks(this.bricks);
    return this.bricks;
  }

  /**
   * 获取积木数量
   */
  async getBricksCount() {
    const bricks = await this.getBricks();
    return bricks.length;
  }

  /**
   * 强制刷新积木数据
   */
  async refresh() {
    console.log('🔄 强制刷新积木数据...');
    this.bricks = [];
    this.initialized = false;
    await this.init();
    return this.bricks;
  }

  /**
   * 清理并修复积木数据
   */
  async cleanAndRepair() {
    console.log('🧹 开始清理并修复积木数据...');

    try {
      // 检查是否有"暂无积木数据"的问题
      const hasInvalidData = this.bricks.some(brick =>
        brick.title === '暂无积木数据' ||
        brick.description === '暂无积木数据' ||
        brick.content === '暂无积木数据'
      );

      if (hasInvalidData) {
        console.log('🔧 检测到"暂无积木数据"问题，执行强制修复...');

        // 清理本地存储中的旧数据
        wx.removeStorageSync('bricks');
        wx.removeStorageSync('bricks_sync_time');

        // 清理全局数据
        const app = getApp();
        if (app.globalData) {
          app.globalData.bricks = [];
        }

        // 重新初始化
        this.bricks = [];
        this.initialized = false;
        await this.init();
      } else {
        console.log('✅ 数据正常，仅进行标准化处理');
        // 仅进行数据标准化
        this.bricks = this.normalizeBricks(this.bricks);
        this.saveToLocal();
        this.updateGlobalData();
      }

      console.log('✅ 积木数据清理修复完成');
      return this.bricks;
    } catch (error) {
      console.error('❌ 积木数据清理修复失败:', error);
      return [];
    }
  }

  /**
   * 强制重新生成默认积木（专门处理"暂无积木数据"问题）
   */
  async forceRegenerateDefaults() {
    console.log('🔄 强制重新生成默认积木数据...');

    try {
      // 完全清理所有数据
      wx.removeStorageSync('bricks');
      wx.removeStorageSync('bricks_sync_time');

      const app = getApp();
      if (app.globalData) {
        app.globalData.bricks = [];
      }

      // 重置管理器状态
      this.bricks = [];
      this.initialized = false;

      // 直接使用默认积木数据
      this.bricks = this.getDefaultBricks();

      // 应用标准化处理
      this.bricks = this.normalizeBricks(this.bricks);

      // 保存数据
      this.saveToLocal();
      this.updateGlobalData();
      this.initialized = true;

      console.log('✅ 默认积木数据重新生成完成');
      return this.bricks;
    } catch (error) {
      console.error('❌ 强制重新生成默认积木失败:', error);
      return [];
    }
  }

  /**
   * 批量添加积木（用于简历解析后的数据保存）
   */
  async addBricksBatch(bricks) {
    console.log('📦 开始批量添加积木:', bricks.length);

    try {
      // 确保BrickManager已初始化
      if (!this.initialized) {
        await this.init();
      }

      // 标准化新积木数据
      const normalizedBricks = bricks.map(brick => ({
        ...brick,
        id: brick.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createTime: brick.createTime || new Date().toISOString(),
        updateTime: new Date().toISOString(),
        userId: wx.getStorageSync('userId') || wx.getStorageSync('openid'),
        usageCount: brick.usageCount || 0,
        isActive: true
      }));

      console.log('📊 数据合并前状态:');
      console.log('- 现有积木数量:', this.bricks.length);
      console.log('- 新增积木数量:', normalizedBricks.length);

      // 🔧 关键修复：合并现有积木和新积木，而不是替换
      const existingBrickIds = new Set(this.bricks.map(brick => brick.id));
      const newBricksToAdd = normalizedBricks.filter(brick => !existingBrickIds.has(brick.id));

      console.log('- 实际新增积木数量:', newBricksToAdd.length);
      console.log('- 重复积木数量:', normalizedBricks.length - newBricksToAdd.length);

      // 检查用户登录状态
      const isLoggedIn = await this.checkUserLoginStatus();
      let cloudSaveSuccess = false;

      if (isLoggedIn && newBricksToAdd.length > 0) {
        // 用户已登录，尝试批量保存到云数据库
        try {
          console.log('☁️ 开始保存到云数据库...');

          for (const brick of newBricksToAdd) {
            // 调用云函数保存单个积木
            const result = await wx.cloud.callFunction({
              name: 'brickManager',
              data: {
                action: 'add',
                data: brick
              }
            });

            // 🔧 修复：使用parseCloudFunctionResult方法正确解析结果
            try {
              const responseData = this.parseCloudFunctionResult(result, '批量保存积木');
              console.log(`✅ 积木已保存到云数据库: ${brick.title}`);
            } catch (parseError) {
              throw new Error(`云数据库保存失败: ${parseError.message}`);
            }
          }

          cloudSaveSuccess = true;
          console.log('✅ 批量保存到云数据库完成');
        } catch (error) {
          console.warn('⚠️ 云数据库批量保存失败:', error);
          cloudSaveSuccess = false;

          // 标记为待同步
          newBricksToAdd.forEach(brick => {
            brick.syncStatus = 'pending';
          });
        }
      } else if (!isLoggedIn && newBricksToAdd.length > 0) {
        console.log('⚠️ 用户未登录，积木将标记为待同步');
        // 标记为待同步
        newBricksToAdd.forEach(brick => {
          brick.syncStatus = 'pending';
        });
      }

      // 合并数据到内存
      if (newBricksToAdd.length > 0) {
        this.bricks = [...this.bricks, ...newBricksToAdd];
        console.log('📊 数据合并后状态:');
        console.log('- 总积木数量:', this.bricks.length);

        // 保存到本地存储和全局状态
        this.saveToLocal();
        this.updateGlobalData();

        console.log('✅ 批量积木数据已保存到本地和内存');
      } else {
        console.log('ℹ️ 没有新积木需要添加');
      }

      return {
        success: true,
        totalCount: this.bricks.length,
        addedCount: newBricksToAdd.length,
        cloudSaveSuccess,
        bricks: this.bricks
      };

    } catch (error) {
      console.error('❌ 批量添加积木失败:', error);
      throw error;
    }
  }

  /**
   * 清空所有积木数据（本地+云端）
   */
  async clearAllBricks() {
    console.log('🗑️ 开始清空所有积木数据（本地+云端）...');

    try {
      let cloudClearSuccess = false;
      let cloudDeletedCount = 0;

      // 1. 检查用户登录状态
      const isLoggedIn = await this.checkUserLoginStatus();

      if (isLoggedIn) {
        // 2. 清空云端数据
        try {
          console.log('☁️ 清空云端积木数据...');
          const result = await wx.cloud.callFunction({
            name: 'brickManager',
            data: {
              action: 'clearAll',
              data: {
                confirm: true
              }
            }
          });

          if (result.result && result.result.body) {
            const response = JSON.parse(result.result.body);
            if (response.success) {
              cloudClearSuccess = true;
              cloudDeletedCount = response.data.removed || 0;
              console.log(`✅ 云端数据清空成功，删除了 ${cloudDeletedCount} 个积木`);
            } else {
              throw new Error(response.error || '云端清空失败');
            }
          } else {
            throw new Error('云函数调用返回格式异常');
          }
        } catch (error) {
          console.error('❌ 云端数据清空失败:', error);
          throw new Error(`云端数据清空失败: ${error.message}`);
        }
      } else {
        console.log('⚠️ 用户未登录，跳过云端数据清空');
        cloudClearSuccess = true; // 未登录时认为云端清空成功
      }

      // 3. 清空本地数据（只有云端清空成功后才执行）
      if (cloudClearSuccess) {
        console.log('💾 清空本地积木数据...');

        // 清空内存中的数据
        this.bricks = [];

        // 清空本地存储
        wx.removeStorageSync('bricks');
        wx.removeStorageSync('bricks_sync_time');

        // 更新全局数据
        this.updateGlobalData();

        console.log('✅ 本地数据清空成功');
      }

      const result = {
        success: true,
        cloudClearSuccess,
        cloudDeletedCount,
        localClearSuccess: true,
        message: isLoggedIn
          ? `成功清空本地和云端数据，共删除 ${cloudDeletedCount} 个积木`
          : '成功清空本地数据'
      };

      console.log('🎉 积木数据清空完成:', result);
      return result;

    } catch (error) {
      console.error('❌ 清空积木数据失败:', error);
      throw error;
    }
  }


}

// 导出类和单例
module.exports = {
  BrickManager,
  default: BrickManager,
  // 创建全局单例
  instance: new BrickManager()
};
